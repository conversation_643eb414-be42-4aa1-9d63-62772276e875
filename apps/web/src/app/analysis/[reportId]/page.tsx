"use client"
import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { trpc } from "@/utils/trpc";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { CheckCircle, Clock, AlertCircle, Loader2, Wifi, WifiOff } from "lucide-react";
import { useEffect, useState } from "react";
import { useRealtimeProgress, useProgressMetrics } from "@/hooks/useRealtimeProgress";

export default function AnalysisProgressPage() {
  const params = useParams();
  const reportId = params.reportId as string;

  // Show loading state while waiting for route parameters
  if (!reportId) {
    return <AnalysisProgressSkeleton />;
  }

  // Validate reportId format (basic UUID validation)
  const isValidReportId = reportId && reportId.length > 0 && reportId !== 'undefined';
  if (!isValidReportId) {
    return (
      <div className="container mx-auto max-w-4xl px-4 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Invalid Report ID
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              The report ID in the URL is invalid. Please check the link and try again.
            </p>
            <Button asChild>
              <a href="/dashboard">Back to Dashboard</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <AnalysisProgressPageContent reportId={reportId} />;
}

function AnalysisProgressPageContent({ reportId }: { reportId: string }) {
  // Use real-time progress tracking
  const { data: realtimeData, isConnected, error: connectionError, reconnect } = useRealtimeProgress(reportId);
  const { overallProgress, agentProgress, reportStatus, isComplete, isFailed } = useProgressMetrics(realtimeData);

  // Fallback query for initial data and when SSE is not available
  const progressQuery = useQuery({
    queryKey: ['getAnalysisProgress', reportId],
    queryFn: () => trpc.getAnalysisProgress.query({ reportId }),
    enabled: !!reportId && !isConnected,
    refetchInterval: !isConnected ? 3000 : false,
  });

  // Query for completed report (only when analysis is done)
  const reportQuery = useQuery({
    queryKey: ['getReport', reportId],
    queryFn: () => trpc.getReport.query({ reportId }),
    enabled: isComplete,
  });

  // Use real-time data if available, otherwise fall back to query data
  const progress = realtimeData?.type === 'progress' ? {
    report: realtimeData.report,
    agentRuns: realtimeData.agentRuns,
    progress: realtimeData.overallProgress,
  } : progressQuery.data;

  const report = reportQuery.data;

  // Note: Refresh interval is handled in the query configuration above

  if (progressQuery.isLoading) {
    return <AnalysisProgressSkeleton />;
  }

  if (progressQuery.error) {
    return (
      <div className="container mx-auto max-w-4xl px-4 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Error Loading Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Failed to load analysis progress. Please try again.
            </p>
            <Button 
              onClick={() => progressQuery.refetch()} 
              className="mt-4"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!progress) {
    return (
      <div className="container mx-auto max-w-4xl px-4 py-8">
        <Card>
          <CardHeader>
            <CardTitle>Analysis Not Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              The requested analysis could not be found.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Analysis Progress</h1>
        <p className="text-muted-foreground">
          Tracking progress for: {progress.report.title}
        </p>
      </div>

      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Overall Progress</span>
            <div className="flex items-center gap-2">
              <Badge variant={getStatusVariant(reportStatus)}>
                {reportStatus}
              </Badge>
              {isConnected ? (
                <div className="flex items-center gap-1 text-xs text-green-600">
                  <Wifi className="h-3 w-3" />
                  Live
                </div>
              ) : (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <WifiOff className="h-3 w-3" />
                  Polling
                </div>
              )}
            </div>
          </CardTitle>
          <CardDescription>
            Multi-agent analysis in progress
            {connectionError && (
              <div className="flex items-center gap-2 mt-2">
                <AlertCircle className="h-4 w-4 text-orange-500" />
                <span className="text-orange-600">Connection issue - using fallback polling</span>
                <Button variant="ghost" size="sm" onClick={reconnect}>
                  Reconnect
                </Button>
              </div>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{overallProgress}%</span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>

          {reportStatus === 'generating' && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Analysis in progress...
              {realtimeData?.timestamp && (
                <span className="text-xs">
                  Last update: {new Date(realtimeData.timestamp).toLocaleTimeString()}
                </span>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Agent Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Agent Progress</CardTitle>
          <CardDescription>
            Individual agent analysis status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {agentProgress.map((agent) => (
              <div key={agent.key} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(agent.status)}
                    <span className="font-medium">{agent.name}</span>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {agent.status}
                  </Badge>
                  {agent.status === 'running' && (
                    <div className="text-xs text-muted-foreground">
                      {agent.progress}%
                    </div>
                  )}
                </div>
                <div className="text-sm text-muted-foreground">
                  {agent.message || agent.step.replace(/_/g, ' ')}
                  {agent.completedAt && (
                    <div className="text-xs text-green-600 mt-1">
                      Completed at {new Date(agent.completedAt).toLocaleTimeString()}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Completed Report */}
      {progress.report.status === 'completed' && report && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Analysis Complete
            </CardTitle>
            <CardDescription>
              Your comprehensive market analysis is ready
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Project Information</h4>
                <div className="space-y-1 text-sm">
                  <p><span className="font-medium">Name:</span> {report.projects.name}</p>
                  <p><span className="font-medium">Type:</span> {report.projects.type}</p>
                  <p><span className="font-medium">Identifier:</span> {report.projects.identifier}</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">Report Details</h4>
                <div className="space-y-1 text-sm">
                  <p><span className="font-medium">Depth:</span> {report.depth}</p>
                  <p><span className="font-medium">Generated:</span> {new Date(report.created_at).toLocaleString()}</p>
                  <p><span className="font-medium">Completed:</span> {new Date(report.completed_at).toLocaleString()}</p>
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button asChild>
                <a href={`/reports/${reportId}`}>
                  View Full Report
                </a>
              </Button>
              <Button variant="outline">
                Download PDF
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Failed Analysis */}
      {progress.report.status === 'failed' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Analysis Failed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              The analysis encountered an error and could not be completed.
            </p>
            <Button>
              Retry Analysis
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function AnalysisProgressSkeleton() {
  return (
    <div className="container mx-auto max-w-6xl px-4 py-8 space-y-6">
      <div className="space-y-2">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-96" />
      </div>
      
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-2 w-full" />
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  switch (status) {
    case 'completed': return 'default';
    case 'generating': return 'secondary';
    case 'failed': return 'destructive';
    default: return 'outline';
  }
}

function getStatusIcon(status: string) {
  switch (status) {
    case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'running': return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    case 'failed': return <AlertCircle className="h-4 w-4 text-destructive" />;
    default: return <Clock className="h-4 w-4 text-muted-foreground" />;
  }
}

function getAgentProgress(agentRuns: any[]) {
  const agentTypes = [
    { key: 'lead_research', name: 'Lead Research', description: 'Project analysis and planning' },
    { key: 'onchain_analytics', name: 'On-Chain Analytics', description: 'Blockchain data analysis' },
    { key: 'social_sentiment', name: 'Social Sentiment', description: 'Community analysis' },
    { key: 'competitor_analysis', name: 'Competitor Analysis', description: 'Market positioning' },
    { key: 'technical_assessment', name: 'Technical Assessment', description: 'Code and security review' },
    { key: 'tokenomics_analysis', name: 'Tokenomics Analysis', description: 'Economic model analysis' },
    { key: 'market_positioning', name: 'Market Positioning', description: 'Strategic recommendations' },
  ];

  return agentTypes.map(agent => {
    const run = agentRuns.find(r => r.agent_type === agent.key);
    return {
      name: agent.name,
      status: run?.status || 'pending',
      message: run?.metrics?.message || agent.description,
    };
  });
}
