# 🤖 CMA Agent System Documentation

## Overview

The Competitive Market Analysis (CMA) platform uses a sophisticated multi-agent orchestration system to analyze Web3 projects. The system consists of 6+ specialized AI agents that work in parallel to gather, analyze, and synthesize comprehensive market intelligence.

## 🏗️ Architecture

### Agent Orchestrator (`orchestrator.ts`)
The central coordinator that manages the entire analysis workflow:

- **Initialization**: Sets up and manages 6 specialized agents
- **Parallel Execution**: Runs agents concurrently for efficiency
- **Progress Tracking**: Real-time status updates and error handling
- **Result Synthesis**: Combines agent outputs into comprehensive reports
- **Database Integration**: Logs all agent runs and results

### Base Agent Class (`base.ts`)
All agents inherit from `BaseAgent` which provides:

- **Common Interface**: Standardized `execute()` method
- **Progress Updates**: Real-time status reporting to database
- **AI Integration**: Structured response generation using OpenRouter/Gemini
- **Error Handling**: Comprehensive logging and error recovery
- **Database Logging**: Automatic run tracking and metrics storage

## 🎯 Specialized Agents

### 1. Lead Research Agent (`lead-research.ts`)
**Role**: Project identification and research coordination

**Tools & Integrations**:
- Exa API for neural search and content discovery
- Firecrawl for website scraping and analysis
- OpenAI embeddings for content analysis

**Key Functions**:
- Analyzes input (domain, Twitter, contract address)
- Identifies project type and basic information
- Creates comprehensive research plans for other agents
- Coordinates task distribution based on project type
- Generates executive summaries

**Output Schema**: Project metadata, research scope, data sources, success criteria

### 2. Social Sentiment Agent (`social-sentiment.ts`)
**Role**: Community engagement and sentiment analysis

**Tools & Integrations**:
- Twitter/X API for engagement metrics
- Discord API for community analysis
- GitHub API for developer activity
- Telegram API for channel metrics
- Reddit API for forum sentiment

**Key Metrics**:
- Follower growth and engagement rates
- Community size and activity levels
- Sentiment scores (-1 to 1 scale)
- Developer activity and contribution metrics
- Influencer network analysis

**Output Schema**: Platform-specific metrics, overall sentiment, community health, growth trends

### 3. Competitor Analysis Agent (`competitor-analysis.ts`)
**Role**: Competitive landscape mapping and positioning

**Tools & Integrations**:
- Exa API for competitor research
- Firecrawl for competitor website analysis
- Market data APIs for metrics comparison

**Key Functions**:
- Identifies direct and indirect competitors
- Analyzes competitive positioning and differentiation
- Compares market share and growth metrics
- Generates SWOT analysis
- Provides strategic recommendations

**Output Schema**: Competitor matrices, market positioning, strategic insights

### 4. Technical Assessment Agent (`technical-assessment.ts`)
**Role**: Technical architecture and security evaluation

**Tools & Integrations**:
- GitHub API for code analysis
- Smart contract audit databases
- Security scanning tools
- Documentation analysis

**Key Areas**:
- Smart contract security and audit status
- Code quality and development activity
- Architecture scalability assessment
- Innovation and technical differentiation
- Security risk evaluation

**Output Schema**: Security scores, development metrics, technical roadmap, risk assessment

### 5. Tokenomics Analysis Agent (`tokenomics-analysis.ts`)
**Role**: Token economics and sustainability analysis

**Tools & Integrations**:
- Blockchain data APIs (DeFiLlama, CoinGecko)
- Token distribution analysis
- Economic modeling tools

**Key Metrics**:
- Token utility and value accrual mechanisms
- Supply dynamics and emission schedules
- Distribution and vesting analysis
- Economic sustainability projections
- Competitive tokenomics comparison

**Output Schema**: Economic models, sustainability metrics, distribution analysis

### 6. Market Positioning Agent (`market-positioning.ts`)
**Role**: Strategic market analysis and opportunities

**Tools & Integrations**:
- Market research APIs
- Industry analysis tools
- TAM/SAM calculation engines

**Key Functions**:
- Total Addressable Market (TAM) assessment
- Industry trends and growth analysis
- Strategic positioning recommendations
- Partnership opportunity identification
- Business development insights

**Output Schema**: Market sizing, strategic recommendations, partnership opportunities

## 🔧 Supporting Infrastructure

### Data Pipeline (`data-pipeline.ts`)
**Purpose**: Centralized data ingestion and processing

**Components**:
- **VectorEmbeddingsService**: Content vectorization for semantic search
- **FirecrawlService**: Web scraping with JavaScript rendering
- **ExaService**: Neural search for research content
- **SocialMediaService**: Multi-platform social data collection
- **BlockchainDataService**: On-chain metrics and analytics

**Features**:
- Batch processing with configurable sizes
- Deduplication and data enrichment
- Rate limiting and retry mechanisms
- Error handling and recovery

### Caching System (`caching-system.ts`)
**Purpose**: Performance optimization and API rate limiting

**Features**:
- Multi-tier caching (memory, database, external)
- TTL-based expiration with smart refresh
- Tag-based cache invalidation
- Specialized caching for blockchain and social data
- Cache warming for popular protocols

### Vector Embeddings (`vector-embeddings.ts`)
**Purpose**: Semantic search and content similarity

**Capabilities**:
- OpenAI text-embedding-3-small model
- Batch embedding generation
- Supabase pgvector storage
- Similarity search and ranking
- Content clustering and analysis

### Report Generation (`report-generation.ts`)
**Purpose**: Comprehensive report assembly and formatting

**Features**:
- Template-based report generation
- Multiple output formats (JSON, PDF, HTML)
- Executive summary generation
- Table of contents and appendices
- Export URL generation

## 🔄 Workflow Process

### 1. Initialization Phase
```
User Input → Lead Research Agent → Project Identification → Research Plan Creation
```

### 2. Parallel Analysis Phase
```
Research Plan → Agent Task Distribution → Parallel Agent Execution
├── Social Sentiment Analysis
├── Competitor Analysis  
├── Technical Assessment
├── Tokenomics Analysis
└── Market Positioning
```

### 3. Synthesis Phase
```
Agent Results → Data Synthesis → Report Generation → Final Output
```

### 4. Progress Tracking
- Real-time status updates for each agent
- Progress percentages and step descriptions
- Error handling and recovery mechanisms
- Database logging for audit trails

## 🎛️ Configuration & Deployment

### Environment Variables
```bash
# AI/LLM Configuration
OPENROUTER_API_KEY=your_key
MODEL=google/gemini-2.5-flash

# Data Sources
EXA_API_KEY=your_key
FIRECRAWL_API_KEY=your_key
OPENAI_API_KEY=your_key

# Database
DATABASE_URL=your_supabase_url
```

### Agent Configuration
- Report depth levels: `quick`, `standard`, `deep`
- Configurable agent priorities based on project type
- Customizable timeout and retry settings
- Flexible data source selection

## 📊 Monitoring & Analytics

### Agent Performance Metrics
- Execution time and success rates
- Data quality and confidence scores
- Source reliability and coverage
- Error patterns and recovery rates

### System Health Monitoring
- Real-time agent status tracking
- Resource utilization monitoring
- API rate limit management
- Cache hit rates and performance

## 🚀 Future Enhancements

### Planned Agent Additions
- **Risk Assessment Agent**: Regulatory and compliance analysis
- **Financial Modeling Agent**: Advanced economic projections
- **Partnership Intelligence Agent**: Strategic alliance identification

### Infrastructure Improvements
- Enhanced caching strategies
- Advanced error recovery mechanisms
- Real-time data streaming capabilities
- Multi-model AI integration

---

*This documentation covers the current agent system architecture. For implementation details, see individual agent files in `packages/agents/src/`.*
