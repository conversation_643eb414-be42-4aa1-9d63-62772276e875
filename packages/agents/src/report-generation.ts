import { z } from 'zod';
import { supabase } from '@cma/database';
import { generateText } from 'ai';
import { models } from '@cma/ai';
import { VectorEmbeddingsService } from './vector-embeddings';

const ReportTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  type: z.enum(['executive_summary', 'detailed_analysis', 'technical_report', 'market_analysis', 'custom']),
  sections: z.array(z.object({
    id: z.string(),
    title: z.string(),
    template: z.string(),
    order: z.number(),
    required: z.boolean(),
    data_sources: z.array(z.string()),
  })),
  metadata: z.record(z.any()).optional(),
});

export type ReportTemplate = z.infer<typeof ReportTemplateSchema>;

export interface ReportGenerationOptions {
  template_id?: string;
  format: 'markdown' | 'html' | 'pdf' | 'json';
  sections?: string[];
  style?: 'professional' | 'technical' | 'executive' | 'detailed';
  include_charts?: boolean;
  include_raw_data?: boolean;
  custom_branding?: {
    logo?: string;
    company_name?: string;
    colors?: Record<string, string>;
  };
}

export interface GeneratedReport {
  id: string;
  project_id: string;
  title: string;
  content: string;
  format: string;
  metadata: {
    generated_at: string;
    template_used: string;
    sections_included: string[];
    data_sources: string[];
    agent_results: Record<string, any>;
    statistics: {
      total_words: number;
      total_pages: number;
      generation_time_ms: number;
    };
  };
  export_urls?: {
    markdown?: string;
    html?: string;
    pdf?: string;
    json?: string;
  };
}

export class ReportGenerationEngine {
  private vectorService: VectorEmbeddingsService;
  private templates: Map<string, ReportTemplate> = new Map();

  constructor() {
    this.vectorService = new VectorEmbeddingsService();
    this.initializeDefaultTemplates();
  }

  async generateReport(
    projectId: string,
    agentResults: Record<string, any>,
    options: ReportGenerationOptions
  ): Promise<GeneratedReport> {
    const startTime = Date.now();
    
    try {
      // Get or create template
      const template = await this.getTemplate(options.template_id || 'detailed_analysis');
      
      // Gather additional context data
      const contextData = await this.gatherContextData(projectId);
      
      // Generate report sections
      const sections = await this.generateSections(
        template,
        agentResults,
        contextData,
        options
      );
      
      // Assemble final report
      const assembledReport = await this.assembleReport(
        template,
        sections,
        agentResults,
        options
      );
      
      // Format report
      const formattedContent = await this.formatReport(assembledReport, options);
      
      // Generate metadata
      const metadata = this.generateMetadata(
        template,
        agentResults,
        sections,
        startTime
      );
      
      // Create report record
      const report: GeneratedReport = {
        id: `report_${projectId}_${Date.now()}`,
        project_id: projectId,
        title: assembledReport.title,
        content: formattedContent,
        format: options.format,
        metadata,
      };
      
      // Store report
      await this.storeReport(report);
      
      // Generate export URLs if needed
      if (options.format !== 'json') {
        report.export_urls = await this.generateExportUrls(report, options);
      }
      
      return report;
      
    } catch (error) {
      console.error('Report generation error:', error);
      throw new Error('Failed to generate report');
    }
  }

  private async getTemplate(templateId: string): Promise<ReportTemplate> {
    // Try to get from memory cache first
    if (this.templates.has(templateId)) {
      return this.templates.get(templateId)!;
    }
    
    // Try to get from database
    const { data, error } = await supabase
      .from('report_templates')
      .select('*')
      .eq('id', templateId)
      .single();
    
    if (error || !data) {
      // Fall back to default template
      return this.templates.get('detailed_analysis')!;
    }
    
    const template = ReportTemplateSchema.parse(data);
    this.templates.set(templateId, template);
    return template;
  }

  private async gatherContextData(projectId: string): Promise<any> {
    // Gather additional context from various sources
    const contextData: any = {
      project_embeddings: [],
      related_projects: [],
      market_context: {},
      timestamp: new Date().toISOString(),
    };

    try {
      // Get project embeddings for context
      contextData.project_embeddings = await this.vectorService.getProjectEmbeddings(projectId);
      
      // Get project metadata
      const { data: project } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single();
      
      if (project) {
        contextData.project_info = project;
      }
      
      // Get related market context
      if (project?.type) {
        contextData.market_context = await this.getMarketContext(project.type);
      }
      
    } catch (error) {
      console.error('Error gathering context data:', error);
    }
    
    return contextData;
  }

  private async generateSections(
    template: ReportTemplate,
    agentResults: Record<string, any>,
    contextData: any,
    options: ReportGenerationOptions
  ): Promise<Record<string, string>> {
    const sections: Record<string, string> = {};
    
    // Filter sections based on options
    const sectionsToGenerate = template.sections.filter(section => 
      !options.sections || options.sections.includes(section.id)
    );
    
    // Generate each section
    for (const section of sectionsToGenerate) {
      try {
        const sectionContent = await this.generateSection(
          section,
          agentResults,
          contextData,
          options
        );
        sections[section.id] = sectionContent;
      } catch (error) {
        console.error(`Error generating section ${section.id}:`, error);
        sections[section.id] = `Error generating ${section.title} section.`;
      }
    }
    
    return sections;
  }

  private async generateSection(
    section: any,
    agentResults: Record<string, any>,
    contextData: any,
    options: ReportGenerationOptions
  ): Promise<string> {
    // Prepare section data
    const sectionData = this.prepareSectionData(section, agentResults, contextData);
    
    // Create prompt for section generation
    const prompt = this.createSectionPrompt(section, sectionData, options);
    
    // Generate section content using AI
    const model = options.style === 'executive' ? models.primary : models.primary;
    
    const { text } = await generateText({
      model,
      prompt,
      maxTokens: 2000,
    });
    
    return text;
  }

  private prepareSectionData(
    section: any,
    agentResults: Record<string, any>,
    contextData: any
  ): any {
    const sectionData: any = {
      section_info: section,
      context: contextData,
    };
    
    // Include relevant agent results
    for (const dataSource of section.data_sources) {
      if (agentResults[dataSource]) {
        sectionData[dataSource] = agentResults[dataSource];
      }
    }
    
    return sectionData;
  }

  private createSectionPrompt(
    section: any,
    sectionData: any,
    options: ReportGenerationOptions
  ): string {
    const style = options.style || 'professional';
    
    return `
Generate a ${style} ${section.title} section for a Web3 project analysis report.

Section Template: ${section.template}

Available Data:
${JSON.stringify(sectionData, null, 2)}

Requirements:
- Write in ${style} style
- Focus on key insights and actionable information
- Use clear, concise language
- Include specific data points and metrics when available
- Structure content with appropriate headings and bullet points
- Ensure factual accuracy based on provided data

Style Guidelines:
- Professional: Balanced, informative, business-focused
- Technical: Detailed, code-oriented, implementation-focused  
- Executive: High-level, strategic, decision-focused
- Detailed: Comprehensive, thorough, analytical

Generate only the section content, not the title.
`;
  }

  private async assembleReport(
    template: ReportTemplate,
    sections: Record<string, string>,
    agentResults: Record<string, any>,
    options: ReportGenerationOptions
  ): Promise<any> {
    const projectInfo = agentResults.lead_research?.data?.project_metadata;
    const title = `${projectInfo?.name || 'Web3 Project'} - Comprehensive Market Analysis Report`;
    
    // Create report structure
    const report = {
      title,
      executive_summary: await this.generateExecutiveSummary(agentResults, options),
      table_of_contents: this.generateTableOfContents(template, sections),
      sections,
      appendices: await this.generateAppendices(agentResults, options),
      metadata: {
        template_name: template.name,
        generated_at: new Date().toISOString(),
      },
    };
    
    return report;
  }

  private async generateExecutiveSummary(
    agentResults: Record<string, any>,
    options: ReportGenerationOptions
  ): Promise<string> {
    const summaryData = {
      project_overview: agentResults.lead_research?.data,
      key_metrics: agentResults.onchain_analytics?.data?.summary,
      market_position: agentResults.market_positioning?.data?.summary,
      sentiment: agentResults.social_sentiment?.data?.overall_sentiment,
      technical_score: agentResults.technical_assessment?.data?.overall_score,
      tokenomics_health: agentResults.tokenomics_analysis?.data?.health_score,
    };
    
    const prompt = `
Create a concise executive summary for a Web3 project analysis report.

Key Information:
${JSON.stringify(summaryData, null, 2)}

Requirements:
- 3-4 paragraphs maximum
- Highlight the most critical findings
- Include overall assessment and recommendation
- Use business-appropriate language
- Focus on investment and strategic implications

Generate only the executive summary content.
`;
    
    const { text } = await generateText({
      model: models.primary,
      prompt,
      maxTokens: 1000,
    });
    
    return text;
  }

  private generateTableOfContents(
    template: ReportTemplate,
    sections: Record<string, string>
  ): string[] {
    const toc = ['Executive Summary'];
    
    template.sections
      .filter(section => sections[section.id])
      .sort((a, b) => a.order - b.order)
      .forEach(section => {
        toc.push(section.title);
      });
    
    toc.push('Appendices');
    
    return toc;
  }

  private async generateAppendices(
    agentResults: Record<string, any>,
    options: ReportGenerationOptions
  ): Promise<Record<string, any>> {
    const appendices: Record<string, any> = {};
    
    if (options.include_raw_data) {
      appendices.raw_data = agentResults;
    }
    
    appendices.methodology = this.generateMethodologyAppendix();
    appendices.data_sources = this.generateDataSourcesAppendix(agentResults);
    appendices.glossary = this.generateGlossaryAppendix();
    
    return appendices;
  }

  private generateMethodologyAppendix(): string {
    return `
## Methodology

This report was generated using an advanced multi-agent AI system that analyzes Web3 projects across six key dimensions:

1. **Lead Research**: Project discovery and initial assessment
2. **On-chain Analytics**: Blockchain data and metrics analysis  
3. **Social Sentiment**: Community engagement and sentiment analysis
4. **Competitor Analysis**: Market positioning and competitive landscape
5. **Technical Assessment**: Code quality and security evaluation
6. **Tokenomics Analysis**: Token economics and sustainability assessment
7. **Market Positioning**: Total addressable market and strategic recommendations

Each agent operates independently and contributes specialized insights that are synthesized into a comprehensive analysis.
`;
  }

  private generateDataSourcesAppendix(agentResults: Record<string, any>): string {
    const sources = new Set<string>();
    
    Object.values(agentResults).forEach((result: any) => {
      if (result?.sources) {
        result.sources.forEach((source: string) => sources.add(source));
      }
    });
    
    return `
## Data Sources

${Array.from(sources).map(source => `- ${source}`).join('\n')}
`;
  }

  private generateGlossaryAppendix(): string {
    return `
## Glossary

**TVL (Total Value Locked)**: The total amount of assets locked in a DeFi protocol
**APY (Annual Percentage Yield)**: The yearly return on investment
**Market Cap**: Total value of all tokens in circulation
**Volume**: Total trading activity over a specific period
**Liquidity**: Ease of buying or selling an asset without affecting its price
**Tokenomics**: The economics and incentive mechanisms of a token
**DAO (Decentralized Autonomous Organization)**: An organization governed by smart contracts
**DeFi (Decentralized Finance)**: Financial services built on blockchain technology
`;
  }

  private async formatReport(report: any, options: ReportGenerationOptions): Promise<string> {
    switch (options.format) {
      case 'markdown':
        return this.formatMarkdown(report, options);
      case 'html':
        return this.formatHTML(report, options);
      case 'pdf':
        return this.formatPDF(report, options);
      case 'json':
        return JSON.stringify(report, null, 2);
      default:
        return this.formatMarkdown(report, options);
    }
  }

  private formatMarkdown(report: any, options: ReportGenerationOptions): string {
    let markdown = `# ${report.title}\n\n`;
    
    // Add metadata
    markdown += `*Generated on ${new Date().toLocaleDateString()}*\n\n`;
    
    // Executive Summary
    markdown += `## Executive Summary\n\n${report.executive_summary}\n\n`;
    
    // Table of Contents
    markdown += `## Table of Contents\n\n`;
    report.table_of_contents.forEach((item: string, index: number) => {
      markdown += `${index + 1}. ${item}\n`;
    });
    markdown += '\n';
    
    // Sections
    Object.entries(report.sections).forEach(([sectionId, content]) => {
      const sectionTitle = sectionId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      markdown += `## ${sectionTitle}\n\n${content}\n\n`;
    });
    
    // Appendices
    if (report.appendices) {
      markdown += `## Appendices\n\n`;
      Object.entries(report.appendices).forEach(([title, content]) => {
        if (typeof content === 'string') {
          markdown += `### ${title.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}\n\n${content}\n\n`;
        }
      });
    }
    
    return markdown;
  }

  private formatHTML(report: any, options: ReportGenerationOptions): string {
    const css = this.generateCSS(options);
    const markdown = this.formatMarkdown(report, options);
    
    // Convert markdown to HTML (basic implementation)
    let html = markdown
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>');
    
    html = `<p>${html}</p>`;
    
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>${report.title}</title>
  <style>${css}</style>
</head>
<body>
  ${html}
</body>
</html>
`;
  }

  private formatPDF(report: any, options: ReportGenerationOptions): string {
    // For PDF generation, we'll return HTML that can be converted to PDF
    // In a real implementation, you'd use a library like Puppeteer or jsPDF
    return this.formatHTML(report, options);
  }

  private generateCSS(options: ReportGenerationOptions): string {
    const colors = options.custom_branding?.colors || {
      primary: '#1a365d',
      secondary: '#2d3748',
      accent: '#3182ce',
    };
    
    return `
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: ${colors.primary};
  border-bottom: 3px solid ${colors.accent};
  padding-bottom: 10px;
}

h2 {
  color: ${colors.secondary};
  margin-top: 30px;
  margin-bottom: 15px;
}

h3 {
  color: ${colors.secondary};
  margin-top: 20px;
}

p {
  margin-bottom: 15px;
}

strong {
  color: ${colors.primary};
}

.metadata {
  font-style: italic;
  color: #666;
  margin-bottom: 30px;
}

.section {
  margin-bottom: 40px;
}
`;
  }

  private generateMetadata(
    template: ReportTemplate,
    agentResults: Record<string, any>,
    sections: Record<string, string>,
    startTime: number
  ): any {
    const content = Object.values(sections).join(' ');
    const wordCount = content.split(/\s+/).length;
    const pageCount = Math.ceil(wordCount / 250); // Estimate ~250 words per page
    
    return {
      generated_at: new Date().toISOString(),
      template_used: template.name,
      sections_included: Object.keys(sections),
      data_sources: Object.keys(agentResults),
      agent_results: agentResults,
      statistics: {
        total_words: wordCount,
        total_pages: pageCount,
        generation_time_ms: Date.now() - startTime,
      },
    };
  }

  private async storeReport(report: GeneratedReport): Promise<void> {
    const { error } = await supabase
      .from('generated_reports')
      .insert({
        id: report.id,
        project_id: report.project_id,
        title: report.title,
        content: report.content,
        format: report.format,
        metadata: report.metadata,
        created_at: new Date().toISOString(),
      });
    
    if (error) {
      console.error('Error storing report:', error);
      throw new Error('Failed to store report');
    }
  }

  private async generateExportUrls(
    report: GeneratedReport,
    options: ReportGenerationOptions
  ): Promise<Record<string, string>> {
    // In a real implementation, these would be actual file storage URLs
    const baseUrl = process.env.REPORT_STORAGE_URL || 'https://storage.example.com';
    
    const urls: Record<string, string> = {};
    
    if (options.format === 'markdown' || options.format === 'html') {
      urls.markdown = `${baseUrl}/reports/${report.id}.md`;
      urls.html = `${baseUrl}/reports/${report.id}.html`;
    }
    
    if (options.format === 'pdf') {
      urls.pdf = `${baseUrl}/reports/${report.id}.pdf`;
    }
    
    urls.json = `${baseUrl}/reports/${report.id}.json`;
    
    return urls;
  }

  private async getMarketContext(projectType: string): Promise<any> {
    // Get market context for the project type
    const marketData = {
      defi: { size: '200B', growth: '45%', maturity: 'growth' },
      nft: { size: '15B', growth: '25%', maturity: 'emerging' },
      gaming: { size: '50B', growth: '35%', maturity: 'emerging' },
      infrastructure: { size: '30B', growth: '55%', maturity: 'growth' },
      dao: { size: '5B', growth: '40%', maturity: 'emerging' },
    };
    
    return marketData[projectType as keyof typeof marketData] || marketData.defi;
  }

  private initializeDefaultTemplates(): void {
    // Executive Summary Template
    this.templates.set('executive_summary', {
      id: 'executive_summary',
      name: 'Executive Summary',
      description: 'High-level overview for decision makers',
      type: 'executive_summary',
      sections: [
        {
          id: 'overview',
          title: 'Project Overview',
          template: 'Brief description of the project and its value proposition',
          order: 1,
          required: true,
          data_sources: ['lead_research'],
        },
        {
          id: 'key_metrics',
          title: 'Key Performance Metrics',
          template: 'Top 5-7 most important metrics and indicators',
          order: 2,
          required: true,
          data_sources: ['onchain_analytics', 'social_sentiment'],
        },
        {
          id: 'recommendation',
          title: 'Investment Recommendation',
          template: 'Clear recommendation with supporting rationale',
          order: 3,
          required: true,
          data_sources: ['market_positioning', 'technical_assessment'],
        },
      ],
    });

    // Detailed Analysis Template
    this.templates.set('detailed_analysis', {
      id: 'detailed_analysis',
      name: 'Detailed Analysis Report',
      description: 'Comprehensive analysis across all dimensions',
      type: 'detailed_analysis',
      sections: [
        {
          id: 'project_overview',
          title: 'Project Overview',
          template: 'Detailed project description, team, and background',
          order: 1,
          required: true,
          data_sources: ['lead_research'],
        },
        {
          id: 'market_analysis',
          title: 'Market Analysis',
          template: 'Market size, positioning, and competitive landscape',
          order: 2,
          required: true,
          data_sources: ['market_positioning', 'competitor_analysis'],
        },
        {
          id: 'technical_assessment',
          title: 'Technical Assessment',
          template: 'Code quality, security, and technical architecture',
          order: 3,
          required: true,
          data_sources: ['technical_assessment'],
        },
        {
          id: 'tokenomics',
          title: 'Tokenomics Analysis',
          template: 'Token distribution, utility, and economic model',
          order: 4,
          required: true,
          data_sources: ['tokenomics_analysis'],
        },
        {
          id: 'onchain_metrics',
          title: 'On-chain Metrics',
          template: 'Blockchain data, TVL, volume, and usage metrics',
          order: 5,
          required: true,
          data_sources: ['onchain_analytics'],
        },
        {
          id: 'social_sentiment',
          title: 'Community & Sentiment',
          template: 'Social media presence, community engagement, sentiment',
          order: 6,
          required: true,
          data_sources: ['social_sentiment'],
        },
        {
          id: 'risks_opportunities',
          title: 'Risks & Opportunities',
          template: 'Key risks and growth opportunities',
          order: 7,
          required: true,
          data_sources: ['technical_assessment', 'market_positioning'],
        },
      ],
    });
  }

  // Public methods for template management

  async createTemplate(template: ReportTemplate): Promise<void> {
    const { error } = await supabase
      .from('report_templates')
      .insert(template);
    
    if (error) {
      throw new Error(`Failed to create template: ${error.message}`);
    }
    
    this.templates.set(template.id, template);
  }

  async getTemplates(): Promise<ReportTemplate[]> {
    const { data, error } = await supabase
      .from('report_templates')
      .select('*');
    
    if (error) {
      throw new Error(`Failed to get templates: ${error.message}`);
    }
    
    return data || [];
  }

  async getReports(projectId: string): Promise<GeneratedReport[]> {
    const { data, error } = await supabase
      .from('generated_reports')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });
    
    if (error) {
      throw new Error(`Failed to get reports: ${error.message}`);
    }
    
    return data || [];
  }
}