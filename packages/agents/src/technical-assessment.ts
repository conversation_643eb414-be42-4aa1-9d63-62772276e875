import { BaseAgent } from './base';
import type { AgentContext, AgentResult } from './base';
import { SYSTEM_PROMPTS } from '@cma/ai';
import { z } from 'zod';

const TechnicalAnalysisSchema = z.object({
  smart_contracts: z.object({
    audit_status: z.enum(['audited', 'partially_audited', 'unaudited']),
    audit_firms: z.array(z.string()),
    vulnerabilities: z.array(z.object({
      severity: z.enum(['critical', 'high', 'medium', 'low']),
      description: z.string(),
      status: z.enum(['open', 'resolved', 'mitigated']),
    })),
    code_quality: z.enum(['excellent', 'good', 'fair', 'poor']),
    upgradeability: z.enum(['immutable', 'proxy', 'governance']),
  }),
  architecture: z.object({
    scalability: z.enum(['high', 'medium', 'low']),
    composability: z.enum(['high', 'medium', 'low']),
    gas_efficiency: z.number().min(0).max(100),
    multi_chain_support: z.boolean(),
    layer2_integration: z.boolean(),
  }),
  development_activity: z.object({
    github_commits: z.number(),
    active_developers: z.number(),
    code_frequency: z.enum(['high', 'medium', 'low']),
    documentation_quality: z.enum(['excellent', 'good', 'fair', 'poor']),
    test_coverage: z.number().min(0).max(100),
  }),
  innovation_score: z.number().min(0).max(100),
  technical_roadmap: z.object({
    upcoming_features: z.array(z.string()),
    timeline: z.string(),
    technical_debt: z.enum(['low', 'medium', 'high']),
  }),
});

export class TechnicalAssessmentAgent extends BaseAgent {
  constructor() {
    super('technical_assessment', SYSTEM_PROMPTS.TECHNICAL_ASSESSMENT);
  }

  async execute(context: AgentContext, input: any): Promise<AgentResult> {
    try {
      const project = input.project || input.initialContext?.projectMetadata;
      
      await this.updateProgress(context.reportId, {
        step: 'analyzing_contracts',
        progress: 20,
        status: 'running',
        message: 'Analyzing smart contract security and quality',
      });

      const contractAnalysis = await this.analyzeSmartContracts(project);

      await this.updateProgress(context.reportId, {
        step: 'assessing_architecture',
        progress: 50,
        status: 'running',
        message: 'Assessing technical architecture and scalability',
      });

      const architectureAnalysis = await this.assessArchitecture(project);

      await this.updateProgress(context.reportId, {
        step: 'evaluating_development',
        progress: 80,
        status: 'running',
        message: 'Evaluating development activity and code quality',
      });

      const developmentAnalysis = await this.evaluateDevelopmentActivity(project);
      const innovationScore = await this.calculateInnovationScore(project);
      const technicalRoadmap = await this.analyzeTechnicalRoadmap(project);

      const comprehensiveAnalysis = {
        smart_contracts: contractAnalysis,
        architecture: architectureAnalysis,
        development_activity: developmentAnalysis,
        innovation_score: innovationScore,
        technical_roadmap: technicalRoadmap,
      };

      await this.updateProgress(context.reportId, {
        step: 'completed',
        progress: 100,
        status: 'completed',
        message: 'Technical assessment completed successfully',
      });

      const result: AgentResult = {
        success: true,
        data: {
          technical_analysis: comprehensiveAnalysis,
          summary: await this.generateSummary(comprehensiveAnalysis),
          recommendations: await this.generateRecommendations(comprehensiveAnalysis),
        },
        sources: this.generateSources(project),
        metrics: {
          contracts_analyzed: project.contracts?.length || 0,
          security_score: this.calculateSecurityScore(contractAnalysis),
          innovation_score: innovationScore,
        },
      };

      await this.logAgentRun(context.reportId, input, result);
      return result;

    } catch (error) {
      const result: AgentResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in technical assessment',
      };
      await this.logAgentRun(context.reportId, input, result);
      return result;
    }
  }

  private async analyzeSmartContracts(project: any): Promise<any> {
    // Mock analysis - would integrate with audit databases and static analysis tools
    return {
      audit_status: project.contracts?.length > 0 ? 'partially_audited' : 'unaudited',
      audit_firms: project.contracts?.length > 0 ? ['ConsenSys Diligence'] : [],
      vulnerabilities: [],
      code_quality: 'good',
      upgradeability: 'proxy',
    };
  }

  private async assessArchitecture(project: any): Promise<any> {
    return {
      scalability: project.type === 'infrastructure' ? 'high' : 'medium',
      composability: project.type === 'defi' ? 'high' : 'medium',
      gas_efficiency: Math.floor(Math.random() * 40) + 60,
      multi_chain_support: Math.random() > 0.6,
      layer2_integration: Math.random() > 0.5,
    };
  }

  private async evaluateDevelopmentActivity(project: any): Promise<any> {
    return {
      github_commits: Math.floor(Math.random() * 1000) + 100,
      active_developers: Math.floor(Math.random() * 20) + 3,
      code_frequency: 'medium',
      documentation_quality: 'good',
      test_coverage: Math.floor(Math.random() * 40) + 60,
    };
  }

  private async calculateInnovationScore(project: any): Promise<number> {
    let score = 50;
    if (project.type === 'infrastructure') score += 20;
    if (project.github) score += 15;
    if (project.description?.includes('innovative')) score += 15;
    return Math.min(100, score);
  }

  private async analyzeTechnicalRoadmap(project: any): Promise<any> {
    return {
      upcoming_features: ['Feature A', 'Feature B', 'Integration C'],
      timeline: 'Q2-Q3 2024',
      technical_debt: 'low',
    };
  }

  private calculateSecurityScore(contractAnalysis: any): number {
    let score = 70;
    if (contractAnalysis.audit_status === 'audited') score += 20;
    if (contractAnalysis.code_quality === 'excellent') score += 10;
    return Math.min(100, score);
  }

  private async generateSummary(analysis: any): Promise<string> {
    return `Technical assessment reveals ${analysis.architecture.scalability} scalability with ${analysis.smart_contracts.code_quality} code quality. Innovation score: ${analysis.innovation_score}/100.`;
  }

  private async generateRecommendations(analysis: any): Promise<string[]> {
    const recommendations = [];
    if (analysis.smart_contracts.audit_status === 'unaudited') {
      recommendations.push('Conduct comprehensive security audit');
    }
    if (analysis.development_activity.test_coverage < 80) {
      recommendations.push('Improve test coverage to enhance reliability');
    }
    return recommendations;
  }

  private generateSources(project: any): string[] {
    const sources = ['GitHub repository analysis'];
    if (project.contracts) sources.push('Smart contract verification');
    return sources;
  }
}